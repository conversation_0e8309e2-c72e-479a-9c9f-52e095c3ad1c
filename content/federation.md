---
title: "Federated Cloud Grid"
description: "How MSPs collaborate through whitesky to build a distributed, sovereign cloud network."
layout: "single"
menu: "main"
weight: 20
---

A growing network of whitesky-powered cloud locations, built by and for MSPs and their clients.

### Built for Collaboration

whitesky enables MSPs to operate sovereign cloud locations under their own brand — and opt into a wider grid of compatible locations. Every participant can:

- Offer their excess capacity (VCPUs, RAM, block/object storage) to peers  
- Buy remote capacity to serve clients in new regions  
- Maintain control over their own infrastructure and customers

### Resell, Expand, Stay Independent

Each partner location remains independently operated, but thanks to federated integration through whitesky:

- Authentication, billing, and automation are unified  
- API keys and user portals span all federated cloud locations  
- Capacity is metered and invoiced transparently  

In addition, **whitesky private clouds** can target these public whitesky cloud locations for:

- Backup and disaster recovery  
- Bursting into additional compute/storage capacity  
- Offloading specific workloads to other trusted regions

This flexibility allows organizations to keep critical operations in-house while leveraging the reach and resilience of the federated network.

### Real-World Collaboration

Whether you're an MSP in Belgium, a telco in Latin America, or a SaaS company scaling in Africa — you can join and contribute to the whitesky.cloud fabric.

**Resell services from fellow providers. Offer local resources globally.**

### Federation Map

{{< whitesky-map >}}

### Current Partners

| Partner                              | Country       | Services Available                         | GPUs | Available now |
|--------------------------------------|---------------|--------------------------------------------|------|---------------|
| [Varity](https://www.varity.cloud)   | Netherlands   | Cloudspaces, Objectspaces, Containerspaces | no   | yes           |
| [CloudCom](https://www.cloudcom.be)  | Belgium       | Cloudspaces, Containerspaces               | yes  | onboarding    |
| [LCI](https://www.lealgroup.mu/)     | Mauritius     | Cloudspaces, Objectspaces, Containerspaces | no   | yes           |
| [AfriQloud](https://www.afriqloud.com) | Uganda      | Cloudspaces, Objectspaces, Containerspaces | no   | yes           |
| [Roke Telecom](https://www.roketelecom.ug) | Uganda  | Cloudspaces, Objectspaces, Containerspaces | yes  | onboarding    |
| [whitesky Poland](https://www.whitesky.pl) (*) | Poland | Cloudspaces, Objectspaces, Containerspaces | yes  | yes           |
| [whitesky](https://whitesky.cloud) (*)        | Belgium       | Cloudspaces, Objectspaces, Containerspaces | yes  | yes           |
| undisclosed                          | Hungary       | Cloudspaces, Containerspaces               | no   | yes           |


_(*) Testing and demonstration_

### 📞 Join the Federation

Want to connect your infrastructure to the grid?  
[Contact us](/contact) to learn more.
