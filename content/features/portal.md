---
title: "Portal"
subtitle: "White-labeled self-service cloud portal with comprehensive management capabilities"
date: 2025-01-15
category: "Management"
icon: "fas fa-tachometer-alt"
summary: "Complete cloud management portal with customer administration, billing, identity management, and comprehensive APIs. Perfect for MSPs and internal IT teams."
tags: ["portal", "management", "billing", "administration", "apis"]
weight: 4
---

# Portal

The whitesky portal is a white-labeled self-service cloud portal that allows internal or external cloud customers to manage their cloud resources across available cloud locations with comprehensive administrative capabilities.

## Cloud Admin: Customer Management and Administration

### Customer Management
Comprehensive tools for managing your cloud customers and their resources.

#### Customer Operations
- **Create/accept/delete customers** with streamlined onboarding
- **Resource pricing** management with flexible pricing models
- **Location access** control for geographic compliance
- **Resource quotas** to manage consumption and costs
- **Customer invoice consultation** for billing transparency

### Billing and Invoicing

#### Automated Billing System
- **Generate monthly invoices** for cloud customers automatically
- **Cloud resource consumption** tracking with detailed usage metrics
- **Microsoft licensed software** billing with daily automated scanning
- **Integration with Octopus.cloud** for SPLA & CSP compliance

#### Revenue Management
- **Transparent pricing** with real-time cost calculation
- **Usage-based billing** for accurate cost allocation
- **Multi-currency support** for global operations
- **Payment processing** integration with multiple providers

### Administration Tools

#### Location Management
- **Manage location resource** standard pricing across all sites
- **Resource allocation** and capacity planning
- **Performance monitoring** across cloud locations
- **Maintenance scheduling** and coordination

#### Communication and Monitoring
- **Send notifications** to customers for important updates
- **Review customer audit records** for compliance and security
- **Sales analysis** with customer cloud resource consumption evolution
- **Software license examination** for Microsoft license usage by customer

### Advanced Settings

#### General Configuration
- **Live chat** integration for customer support
- **Self-registration + payment + approval** workflows
- **Notifications forwarding** to external systems
- **Show/hide pricing** options for different customer tiers
- **License compliance issue notifications** for proactive management

#### Compliance and Integration
- **Microsoft SPLA license** configuration and management
- **Invoice settings** customization for branding
- **Portal branding** for white-label deployments
- **Payment provider** integration and configuration
- **Audit record forwarding** to external compliance systems

#### Resource and Support Management
- **DNS resource settings** for automated domain management
- **Default resource quotas** for new customers
- **Support configuration** for help desk integration
- **Emergency notifications** for critical system alerts
- **License compliance overview** for Windows VMs that cannot be automatically scanned

## Customer Admin Section

### SSL Certificate Management
Comprehensive SSL certificate lifecycle management for secure communications.

#### Certificate Operations
- **Customer SSL certificate store** for use in reverse proxies and kubernetes clusters
- **Add SSL certificates** with validation and deployment
- **Update SSL certificates** with automatic propagation to resources
- **Automated renewal** integration with Let's Encrypt
- **Certificate monitoring** and expiration alerts

### Access Control and Security

#### Role-Based Access Control
- **Define roles and permissions** for fine-grained resource access
- **Hierarchical permissions** for complex organizational structures
- **Resource-level access** control for specific services
- **Audit trail** for all access control changes

#### DNS and Network Management
- **DNS settings** for automated DNS records for cloud resources
- **Domain management** with automatic record creation
- **Network access** control and firewall management
- **VPN configuration** for secure remote access

#### Compliance and Monitoring
- **License notifications** to get warnings when users deploy Microsoft licensed software
- **Audit record review** for security and compliance monitoring
- **Emergency notifications** for critical resource availability issues
- **Compliance reporting** for regulatory requirements

## Identity and Access Management

### Security Framework
Enterprise-grade identity and access management with comprehensive security features.

#### Authentication System
- **Separate deployed OpenID/OAUTH2** Identity access manager
- **2FA always required** for enhanced security via:
  - Email verification
  - Phone number (SMS) verification
  - Authenticator app integration
- **Hierarchical Organization** based access control
- **JWT support** for modern authentication workflows

#### User Management
- **Self-registration** capabilities for streamlined onboarding
- **SSO integration** via Microsoft Account/Google Account (coming soon)
- **User lifecycle management** with automated provisioning
- **Session management** with configurable timeout policies

## Data Protection and Recovery

### Recycle Bin
Comprehensive data protection with soft deletion and recovery capabilities.

#### Resource Protection
- **Soft deleted cloud resources** remain in recycle bin for 7 days:
  - Cloudspaces and objectspaces
  - Virtual machines and buckets
  - vDisks and VM Images
  - CDROM images and vGPUs
- **Permanent deletion** after retention period
- **Ability to empty recycle bin** for immediate cleanup
- **Recovery options** for accidentally deleted resources

## Tools and APIs

### Developer Integration
Complete API ecosystem for automation and integration.

#### API Capabilities
- **100% complete API** - UI is built on top of the API
- **API transparency** - UI shows which APIs it used for building the interface
- **Swagger UI** for exploring and testing the API
- **RESTful design** with consistent patterns and responses

#### Development Tools
- **CLI for Windows, Mac and Linux** for command-line automation
- **Terraform provider** for infrastructure as code
- **Ansible support** in opensource for configuration management
- **SDK libraries** for popular programming languages

#### Integration Benefits
- **Automation capabilities** for DevOps workflows
- **Third-party integrations** with existing tools
- **Custom application development** using the platform APIs
- **Monitoring and alerting** integration with external systems

## Why Choose Our Portal?

The whitesky portal provides a complete cloud management solution that scales from small internal teams to large MSP operations. With comprehensive billing, security, and automation capabilities, it's the foundation for successful cloud service delivery.

Whether you're managing internal resources or providing cloud services to customers, our portal gives you the tools and flexibility you need to succeed in today's competitive cloud market.
