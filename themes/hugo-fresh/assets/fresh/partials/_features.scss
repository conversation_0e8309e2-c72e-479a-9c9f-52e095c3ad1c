/* ==========================================================================
Features Grid Styles
========================================================================== */

// Features Grid Layout
.features-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: flex-start;
    margin-top: 3rem;
}

.feature-item {
    flex: 0 0 calc(33.333% - 1.5rem);
    display: flex;
    
    .feature-card {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        
        // &:hover {
        //     transform: translateY(-4px);
        //     box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        // }
    }
}

// Feature Card Components
.feature-icon-section {
    padding: 2rem 1.5rem 1rem;
}

.feature-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding-top: 0;
}

.feature-category {
    margin-bottom: 1rem;
}

.feature-description {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    text-align: center;
}

.feature-summary {
    flex-grow: 1;
}

.feature-action {
    margin-top: auto;
    padding-top: 1.5rem;
}

// Responsive Design
@media (max-width: 1100px) {
    .features-grid {
        gap: 2rem !important;
        justify-content: space-around !important;
    }
    
    .feature-item {
        flex: 0 0 calc(50% - 1rem) !important;
    }
}

@media (max-width: 768px) {
    .features-grid {
        gap: 1.5rem !important;
    }
    
    .feature-item {
        flex: 0 0 100% !important;
    }
}

/* ==========================================================================
Feature Single Page Styles
========================================================================== */

// Feature Icon on Single Pages
.feature-icon {
    margin-bottom: 1.5rem;
}

// Feature Meta Information
.feature-meta {
    margin-top: 1rem;
}

// Feature Footer
.feature-footer {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #dbdbdb;
}

// Related Features Section
.related-features {
    margin-top: 4rem;
    
    .related-feature-card {
        height: 100%;
        
        .card-content {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }
        
        .related-feature-summary {
            flex-grow: 1;
        }
        
        .related-feature-action {
            margin-top: 1rem;
        }
    }
}
