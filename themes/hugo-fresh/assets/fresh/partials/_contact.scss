/* ==========================================================================
Contact Page Styles
========================================================================== */

.contact-page {
  .contact-info {
    background: $section-grey;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;

    h2 {
      color: $blue-grey;
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
    }

    h3 {
      color: $blue-grey;
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 1rem;
      margin-top: 2rem;
    }

    p {
      color: $basaltic-grey;
      line-height: 1.6;
      margin-bottom: 1rem;
    }

    ul {
      list-style: none;
      padding: 0;
      margin-bottom: 1.5rem;

      li {
        color: $basaltic-grey;
        line-height: 1.8;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;

        &::before {
          content: "📧";
          margin-right: 0.75rem;
          font-size: 1.1rem;
        }

        &:nth-child(2)::before {
          content: "📞";
        }

        a {
          color: $primary;
          text-decoration: none;
          font-weight: 500;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    // Contact table styling
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
      background: $white;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      thead {
        background: $primary;
        color: $white;

        th {
          padding: 1rem 0.75rem;
          text-align: left;
          font-weight: 600;
          font-size: 0.95rem;
        }
      }

      tbody {
        tr {
          border-bottom: 1px solid $fade-grey;

          &:last-child {
            border-bottom: none;
          }

          &:nth-child(even) {
            background: $smoke-white;
          }

          td {
            padding: 1rem 0.75rem;
            color: $basaltic-grey;
            font-size: 0.95rem;

            a {
              color: $primary;
              text-decoration: none;
              font-weight: 500;

              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }

    // Management contacts list
    .management-contacts {
      margin-top: 1.5rem;

      ul {
        li {
          &::before {
            content: "👤";
          }
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 1100px) {
    .contact-info {
      padding: 1.5rem;

      table {
        font-size: 0.9rem;

        thead th,
        tbody td {
          padding: 0.75rem 0.5rem;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .contact-info {
      padding: 1rem;

      h2 {
        font-size: 1.3rem;
      }

      h3 {
        font-size: 1.2rem;
      }

      ul li {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;

        &::before {
          margin-bottom: 0.25rem;
        }
      }

      // Make table responsive
      table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
        box-shadow: none;

        thead,
        tbody,
        th,
        td,
        tr {
          display: block;
        }

        thead tr {
          position: absolute;
          top: -9999px;
          left: -9999px;
        }

        tbody tr {
          border: 1px solid $fade-grey;
          margin-bottom: 1rem;
          border-radius: 6px;
          padding: 1rem;
          background: $white;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:nth-child(even) {
            background: $white;
          }
        }

        tbody td {
          border: none;
          position: relative;
          padding: 0.75rem 0.5rem 0.75rem 40%;
          white-space: normal;
          text-align: left;
          border-bottom: 1px solid $fade-grey;

          &:last-child {
            border-bottom: none;
          }

          &:before {
            content: attr(data-label) ": ";
            position: absolute;
            left: 6px;
            width: 35%;
            padding-right: 10px;
            white-space: nowrap;
            font-weight: 600;
            color: $blue-grey;
            font-size: 0.9rem;
          }

          a {
            word-break: break-word;
          }
        }
      }
    }
  }
}

// Additional styles for contact form section when used
.contact-form-section {
  background: $white;

  .title-wrapper {
    margin-bottom: 2rem;
  }

  .form-wrapper {
    background: $section-grey;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    .form-wrapper {
      padding: 1rem;
    }
  }
}
