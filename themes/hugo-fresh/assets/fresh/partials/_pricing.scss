/* ==========================================================================
Pricing Page Styles
========================================================================== */

.pricing-page {
  .quote-form {
    background: $white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 2rem;

    .form-title {
      color: $blue-grey;
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .field {
      margin-bottom: 1.5rem;

      .label {
        color: $blue-grey;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1rem;
      }

      .input, .select select, .textarea {
        border: 2px solid $fade-grey;
        border-radius: 6px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        color: $basaltic-grey;

        &:focus {
          border-color: $primary;
          box-shadow: 0 0 0 0.125em rgba(0, 148, 202, 0.25);
          outline: none;
        }

        &::placeholder {
          color: $placeholder;
        }
      }

      .select {
        width: 100%;

        select {
          width: 100%;
          cursor: pointer;
        }
      }

      .textarea {
        min-height: 120px;
        resize: vertical;
      }
    }

    .checkbox-field {
      display: flex;
      align-items: flex-start;
      margin-bottom: 1rem;

      input[type="checkbox"] {
        margin-right: 0.75rem;
        margin-top: 0.25rem;
        transform: scale(1.2);
        accent-color: $primary;
      }

      label {
        color: $blue-grey;
        font-size: 0.95rem;
        line-height: 1.4;
        cursor: pointer;
      }
    }

    .form-note {
      font-size: 0.85rem;
      color: $muted-grey;
      font-style: italic;
      margin-bottom: 1.5rem;
    }

    .submit-button {
      width: 100%;
      margin-top: 1rem;
      font-size: 1.1rem;
      font-weight: 600;
      padding: 1rem 2rem;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .info-section {
    margin-top: 3rem;

    h2 {
      color: $blue-grey;
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    h3 {
      color: $blue-grey;
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 1rem;
      margin-top: 2rem;
    }

    p {
      color: $basaltic-grey;
      line-height: 1.6;
      margin-bottom: 1rem;
    }

    ul {
      margin-left: 1.5rem;
      margin-bottom: 1.5rem;

      li {
        color: $basaltic-grey;
        line-height: 1.6;
        margin-bottom: 0.5rem;
      }
    }
  }

  // Success and error messages
  .form-message {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 6px;
    display: none;

    &.success {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }

    &.error {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }

    .close-btn {
      float: right;
      background: none;
      border: none;
      font-size: 1.2rem;
      cursor: pointer;
      color: inherit;
      opacity: 0.7;

      &:hover {
        opacity: 1;
      }
    }
  }

  // Responsive design
  @media (max-width: 1100px) {
    .quote-form {
      padding: 1.5rem;
      margin-left: 1rem;
      margin-right: 1rem;
    }
  }

  @media (max-width: 768px) {
    .quote-form {
      padding: 1rem;
      margin-top: 1rem;
      margin-left: 0.5rem;
      margin-right: 0.5rem;

      .form-title {
        font-size: 1.3rem;
      }

      .field {
        margin-bottom: 1rem;

        .label {
          font-size: 0.95rem;
        }

        .input, .select select, .textarea {
          font-size: 0.95rem;
          padding: 0.65rem 0.85rem;
        }
      }

      .checkbox-field {
        flex-direction: column;
        align-items: flex-start;

        input[type="checkbox"] {
          margin-right: 0;
          margin-bottom: 0.5rem;
        }
      }

      .submit-button {
        font-size: 1rem;
        padding: 0.85rem 1.5rem;
      }
    }

    .info-section {
      margin-top: 2rem;
      padding: 0 0.5rem;

      h2 {
        font-size: 1.3rem;
      }

      h3 {
        font-size: 1.2rem;
      }
    }
  }

  @media (max-width: 480px) {
    .quote-form {
      padding: 0.75rem;
      margin-left: 0.25rem;
      margin-right: 0.25rem;

      .form-title {
        font-size: 1.2rem;
        margin-bottom: 1rem;
      }

      .field {
        margin-bottom: 0.75rem;

        .input, .select select, .textarea {
          font-size: 0.9rem;
          padding: 0.6rem 0.75rem;
        }
      }

      .submit-button {
        font-size: 0.95rem;
        padding: 0.75rem 1.25rem;
      }
    }
  }
}
