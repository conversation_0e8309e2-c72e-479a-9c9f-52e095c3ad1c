{{ define "main" }}

{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<section class="section is-medium">
  <div class="container">
    <!-- Feature Header -->
    <div class="columns">
      <div class="column is-8 is-offset-2">
        <div class="has-text-centered">
          {{ if .Params.icon }}
          <div class="feature-icon" style="margin-bottom: 1.5rem;">
            <span class="icon is-large">
              <i class="{{ .Params.icon }} fa-4x  primary-color"></i>
            </span>
          </div>
          {{ end }}

          <h1 class="title is-2 has-text-weight-bold">{{ .Title }}</h1>
          {{ if .Params.subtitle }}
          <h2 class="subtitle is-4 has-text-grey">{{ .Params.subtitle }}</h2>
          {{ end }}

          <div class="feature-meta">
            {{ if .Params.category }}
            <p class="has-text-grey">
              <span class="tag is-primary is-light">{{ .Params.category }}</span>
            </p>
            {{ end }}
          </div>
        </div>

        <div class="divider is-centered" style="margin-top: 1rem;"></div>
      </div>
    </div>

    <!-- Feature Content -->
    <div class="columns">
      <div class="column is-8 is-offset-2">
        <div class="content is-medium">
          {{ .Content }}
        </div>

        <!-- Feature Footer -->
        <div class="feature-footer" style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #dbdbdb;">
          <div class="level">
            <div class="level-left">
              {{ if .Params.tags }}
              <div class="level-item">
                <div class="field is-grouped is-grouped-multiline">
                  {{ range .Params.tags }}
                  <div class="control">
                    <div class="tags has-addons">
                      <span class="tag is-light">{{ . }}</span>
                    </div>
                  </div>
                  {{ end }}
                </div>
              </div>
              {{ end }}
            </div>
            <div class="level-right">
              <div class="level-item">
                <div class="field is-grouped">
                  <p class="control">
                    <a class="button primary-btn raised" href="/features">
                      <span class="icon">
                        <i class="fas fa-arrow-left"></i>
                      </span>
                      <span>Back to Features</span>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Related Features -->
    {{ $related := where (where .Site.RegularPages "Section" "features") ".Permalink" "!=" .Permalink | first 3 }}
    {{ if $related }}
    <div class="columns related-features">
      <div class="column is-10 is-offset-1">
        <h3 class="title is-4 has-text-centered">Related Features</h3>
        <div class="divider is-centered"></div>

        <div class="columns is-multiline" style="margin-top: 2rem;">
          {{ range $related }}
          <div class="column is-one-third">
            <div class="card related-feature-card">
              {{ if .Params.icon }}
              <div class="card-content has-text-centered" style="padding-bottom: 0;">
                <span class="icon is-large">
                  <i class="{{ .Params.icon }} fa-2x  primary-color"></i>
                </span>
              </div>
              {{ end }}

              <div class="card-content">
                <p class="title is-6">
                  <a href="{{ .Permalink }}" class="has-text-dark">{{ .Title }}</a>
                </p>
                {{ if .Params.category }}
                <p class="subtitle is-7 has-text-grey">
                  <span class="tag is-light">{{ .Params.category }}</span>
                </p>
                {{ end }}
                {{ if .Params.summary }}
                <p class="is-size-7 related-feature-summary">{{ .Params.summary | truncate 100 }}</p>
                {{ end }}
                <div class="related-feature-action">
                  <a href="{{ .Permalink }}" class="button is-small primary-btn">
                    <span>Learn More</span>
                    <span class="icon">
                      <i class="fas fa-arrow-right"></i>
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
          {{ end }}
        </div>
      </div>
    </div>
    {{ end }}
  </div>
</section>

{{ partial "footer.html" . }}

{{ end }}
