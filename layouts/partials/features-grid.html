<!-- Features Grid Partial -->
{{ if .Pages }}
<div class="features-grid">
  {{ range .Pages.ByWeight }}
  <div class="feature-item">
    <div class="card feature-card">
      {{ if .Params.icon }}
      <div class="card-content has-text-centered feature-icon-section">
        <span class="icon is-large">
          <i class="{{ .Params.icon }} fa-3x  primary-color"></i>
        </span>
      </div>
      {{ end }}

      <div class="card-content feature-content">
        <div class="media">
          <div class="media-content">
            <p class="title is-4 has-text-centered">
              <a href="{{ .Permalink }}" class="has-text-dark">{{ .Title }}</a>
            </p>
            {{ if .Params.category }}
            <p class="subtitle is-6 has-text-grey has-text-centered feature-category">
              <span class="tag is-primary is-light">{{ .Params.category }}</span>
            </p>
            {{ end }}
          </div>
        </div>

        <div class="content feature-description">
          <div class="feature-summary">
            {{ if .Params.summary }}
              <p>{{ .Params.summary }}</p>
            {{ else }}
              <p>{{ .Summary }}</p>
            {{ end }}
          </div>

          <div class="feature-action">
            <a href="{{ .Permalink }}" class="button primary-btn raised">
              <span>Learn More</span>
              <span class="icon">
                <i class="fas fa-arrow-right"></i>
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  {{ end }}
</div>
{{ else }}
<div class="columns">
  <div class="column has-text-centered">
    <p class="subtitle is-5 has-text-grey">Feature pages are being prepared. Check back soon!</p>
  </div>
</div>
{{ end }}
