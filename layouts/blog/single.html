{{ define "main" }}

{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<section class="section is-medium">
  <div class="container">
    <!-- Article Header -->
    <div class="columns">
      <div class="column is-8 is-offset-2">
        <div class="has-text-centered">
          <h1 class="title is-2 has-text-weight-bold">{{ .Title }}</h1>
          {{ if .Params.subtitle }}
          <h2 class="subtitle is-4 has-text-grey">{{ .Params.subtitle }}</h2>
          {{ end }}
          
          <div class="article-meta">
            <p class="has-text-grey">
              <time datetime="{{ .Date.Format "2006-01-02" }}">
                {{ .Date.Format "January 2, 2006" }}
              </time>
              {{ if .Params.author }}
              <span class="has-text-grey-light"> • </span>
              <span>{{ .Params.author }}</span>
              {{ end }}
              <span class="has-text-grey-light"> • </span>
              <span>{{ .ReadingTime }} min read</span>
            </p>
          </div>
          
          {{ if .Params.tags }}
          <div class="tags is-centered" style="margin-top: 1rem;">
            {{ range .Params.tags }}
            <span class="tag is-primary is-light">{{ . }}</span>
            {{ end }}
          </div>
          {{ end }}
        </div>
        
        <div class="divider  is-centered" style="margin-top: 1rem;"></div>
      </div>
    </div>

    <!-- Featured Image -->
    {{ if .Params.featured_image }}
    <div class="columns">
      <div class="column is-10 is-offset-1">
        <figure class="image">
          <img src="{{ .Params.featured_image | relURL }}" alt="{{ .Title }}" style="border-radius: 6px;">
        </figure>
      </div>
    </div>
    {{ end }}

    <!-- Article Content -->
    <div class="columns">
      <div class="column is-8 is-offset-2">
        <div class="content is-medium">
          {{ .Content }}
        </div>
        
        <!-- Article Footer -->
        <div class="article-footer" style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #dbdbdb;">
          <div class="level">
            <div class="level-left">
              {{ if .Params.author }}
              <div class="level-item">
                <div class="media">
                  <div class="media-content">
                    <p class="title is-6">Author: {{ .Params.author }}</p>
                  </div>
                </div>
              </div>
              {{ end }}
            </div>
            <div class="level-right">
              <div class="level-item">
                <div class="field is-grouped">
                  <p class="control">
                    <a class="button primary-btn raised" href="/blog">
                      <span class="icon">
                        <i class="fas fa-arrow-left"></i>
                      </span>
                      <span>Back to Blogs</span>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Related Posts -->
    {{ $related := .Site.RegularPages.Related . | first 3 }}
    {{ if $related }}
    <div class="columns" style="margin-top: 4rem;">
      <div class="column is-10 is-offset-1">
        <h3 class="title is-4 has-text-centered">Related Articles</h3>
        <div class="divider"></div>
        
        <div class="columns is-multiline">
          {{ range $related }}
          <div class="column is-one-third">
            <div class="card">
              {{ if .Params.featured_image }}
              <div class="card-image">
                <figure class="image is-16by9">
                  <img src="{{ .Params.featured_image | relURL }}" alt="{{ .Title }}">
                </figure>
              </div>
              {{ end }}
              
              <div class="card-content">
                <p class="title is-6">
                  <a href="{{ .Permalink }}" class="has-text-dark">{{ .Title }}</a>
                </p>
                <p class="subtitle is-7 has-text-grey">
                  {{ .Date.Format "January 2, 2006" }}
                </p>
                {{ if .Params.summary }}
                <p class="is-size-7">{{ .Params.summary | truncate 100 }}</p>
                {{ end }}
              </div>
            </div>
          </div>
          {{ end }}
        </div>
      </div>
    </div>
    {{ end }}
  </div>
</section>

{{ if .Params.include_footer }}
{{ partial "footer.html" . }}
{{ end }}

{{ end }}
