{{ define "main" }}

{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<section class="section is-medium" id="blog">
  <div class="container">
    <div class="columns">
      <div class="column is-centered-tablet  has-text-centered">
        <h1 class="title section-title">{{ .Title }}</h1>
        <h5 class="subtitle is-5 is-muted">{{ .Params.Subtitle }}</h5>
        <div class="divider is-centered"></div>
      </div>
    </div>

    <!-- Blog Posts Grid -->
    <div class="blog-grid" style="display: flex; flex-wrap: wrap; gap: 4rem; justify-content: space-between;">
      {{ range .Pages }}
      <div class="blog-item" style="flex: 0 0 calc(50% - 2rem); display: flex;">
        <div class="card" style="display: flex; flex-direction: column; width: 100%; height: 100%;">
          {{ if .Params.featured_image }}
          <div class="card-image">
            <figure class="image is-16by9">
              <img src="{{ .Params.featured_image | relURL }}" alt="{{ .Title }}">
            </figure>
          </div>
          {{ end }}

          <div class="card-content" style="display: flex; flex-direction: column; flex-grow: 1;">
            <div class="media">
              <div class="media-content">
                <p class="title is-4">
                  <a href="{{ .Permalink }}" class="has-text-dark">{{ .Title }}</a>
                </p>
                <p class="subtitle is-6 has-text-grey">
                  <time datetime="{{ .Date.Format "2006-01-02" }}">
                    {{ .Date.Format "January 2, 2006" }}
                  </time>
                  {{ if .Params.author }}
                  <span class="has-text-grey-light"> • </span>
                  <span>{{ .Params.author }}</span>
                  {{ end }}
                </p>
              </div>
            </div>

            <div class="content" style="flex-grow: 1; display: flex; flex-direction: column;">
              <div style="flex-grow: 1;">
                {{ if .Params.summary }}
                  <p>{{ .Params.summary }}</p>
                {{ else }}
                  <p>{{ .Summary }}</p>
                {{ end }}
              </div>

              {{ if .Params.tags }}
              <div class="tags" style="margin-bottom: 1rem;">
                {{ range .Params.tags }}
                <span class="tag is-light">{{ . }}</span>
                {{ end }}
              </div>
              {{ end }}

              <div style="margin-top: auto;">
                <a href="{{ .Permalink }}" class="button primary-btn raised">
                  <span class="icon">
                    <i class="fas fa-arrow-right"></i>
                  </span>
                  <span>Read More</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      {{ end }}
    </div>

    {{ if eq (len .Pages) 0 }}
    <div class="columns">
      <div class="column has-text-centered">
        <p class="subtitle is-5 has-text-grey">No blog posts available yet. Check back soon!</p>
      </div>
    </div>
    {{ end }}
  </div>
</section>

{{ partial "footer.html" . }}

{{ end }}
