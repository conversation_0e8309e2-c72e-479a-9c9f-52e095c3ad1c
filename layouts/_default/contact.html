{{ define "main" }}

{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<section class="section is-medium">
  <div class="container">
    <div class="columns">
      <div class="column is-centered-tablet-portrait">
        <h1 class="title section-title">{{ .Title }}</h1>
        <h5 class="subtitle is-5 is-muted">{{ .Params.description }}</h5>
        <div class="divider"></div>
      </div>
    </div>

    <div class="content">
      {{ .Content }}
    </div>
  </div>
</section>

<!-- Contact Form Section -->
<section class="section section-light-grey is-medium contact-form-section" id="contact-form">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2 is-spaced">Send us a Message</h2>
      <h3 class="subtitle is-5 is-muted">We'd love to hear from you</h3>
      <div class="divider is-centered"></div>
    </div>

    <div class="content-wrapper">
      <div class="columns">
        <div class="column is-6 is-offset-3">
          <div class="form-wrapper">
            <!-- Success Message (initially hidden) -->
            <div
              id="form-success"
              class="notification is-success"
              style="display: none"
            >
              <button
                class="delete"
                onclick="document.getElementById('form-success').style.display='none'"
              ></button>
              <strong>Thank you!</strong> Your message has been sent successfully.
              We'll get back to you soon.
            </div>

            <!-- Error Message (initially hidden) -->
            <div
              id="form-error"
              class="notification is-danger"
              style="display: none"
            >
              <button
                class="delete"
                onclick="document.getElementById('form-error').style.display='none'"
              ></button>
              There was a problem submitting your form. Please try again.
            </div>

            <form
              id="contact-form"
              action="{{ .Site.Params.section5.action }}"
              method="{{ .Site.Params.section5.method }}"
            >
              <div class="columns is-multiline">
                <div class="column is-6">
                  <input
                    class="input is-medium"
                    name="name"
                    type="text"
                    placeholder="Enter your name"
                    required
                  />
                </div>
                <div class="column is-6">
                  <input
                    class="input is-medium"
                    name="email"
                    type="email"
                    placeholder="Enter your email address"
                    required
                  />
                </div>
                <div class="column is-12">
                  <input
                    class="input is-medium"
                    name="subject"
                    type="text"
                    placeholder="Subject"
                  />
                </div>
                <div class="column is-12">
                  <textarea
                    class="textarea"
                    name="message"
                    rows="10"
                    placeholder="Write something..."
                    required
                  ></textarea>
                </div>
                <!-- Formspree honeypot field to prevent spam -->
                <input type="text" name="_gotcha" style="display: none" />
                <div class="form-footer has-text-centered mt-10">
                  <button
                    id="submit-button"
                    class="button cta is-large primary-btn raised is-clear g-recaptcha"
                    data-sitekey="{{ .Site.Params.section5.recaptcha_site_key }}"
                    data-callback="onSubmit"
                  >
                    Send Message
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{{ partial "section5.html" . }}
{{ partial "footer.html" . }}

{{ end }}
